package service

import (
	"context"
	"fmt"
	"path/filepath"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	apputil "gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/util"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/node/util"
	"gitlab.bingosoft.net/cloud-public/logger"
	"golang.org/x/sync/errgroup"
)

// DockerNodeManager Docker节点管理器
type DockerNodeManager struct {
	BaseDir string
}

// NewDockerNodeManager 创建DockerNodeManager实例
func NewDockerNodeManager(baseDir string) (NodeManagerInterface, error) {
	return &DockerNodeManager{BaseDir: baseDir}, nil
}

// Init 初始化节点
func (d *DockerNodeManager) Init(ctx context.Context) error {
	logger.Infof(ctx, "Starting Docker node initialization")

	// 查找所有脚本
	scripts, err := util.FindNodeScripts(constants.DeployModeSingle, d.BaseDir)
	if err != nil {
		return fmt.Errorf("failed to find node scripts in base directory %s: %w", d.BaseDir, err)
	}

	if len(scripts) == 0 {
		logger.Warnf(ctx, "No node initialization scripts found")
		return nil
	}

	logger.Infof(ctx, "Found %d node initialization scripts", len(scripts))

	// 创建错误组用于并发控制
	g, ctx := errgroup.WithContext(ctx)

	// 并发执行所有脚本
	for _, script := range scripts {
		script := script // 创建副本避免闭包问题
		g.Go(func() error {
			logger.Debugf(ctx, "Starting script execution: %s", script)

			opt := &apputil.ExecOption{
				FilePath: script,
				CmdName:  constants.ShellCmd,
				Args:     []string{script},
				WorkDir:  filepath.Dir(script),
				SetHome:  true,
			}
			if err := apputil.ExecFileIfExists(ctx, opt); err != nil {
				logger.Errorf(ctx, "Script execution failed %s: %v", script, err)
				return fmt.Errorf("script execution failed %s: %w", script, err)
			}

			logger.Infof(ctx, "Script executed successfully: %s", script)
			return nil
		})
	}

	// 等待所有脚本执行完成
	if err := g.Wait(); err != nil {
		return fmt.Errorf("node initialization failed: %w", err)
	}

	logger.Infof(ctx, "Docker node initialization completed")
	return nil
}
