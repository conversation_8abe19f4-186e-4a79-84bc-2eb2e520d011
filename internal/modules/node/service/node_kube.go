package service

import (
	"context"
	"fmt"
	"path/filepath"

	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/app/constants"
	"gitlab.bingosoft.net/ccpc-project/706/dep-tool/internal/modules/node/util"
	"gitlab.bingosoft.net/cloud-public/logger"
	"gitlab.bingosoft.net/cloud-public/utils/sshpass"
	"golang.org/x/sync/errgroup"
)

// KubeNodeManager kube节点管理器
type KubeNodeManager struct {
	NodeIPs    []string
	DeployMode constants.DeployModeType
	BaseDir    string
}

// NewKubeNodeManager 创建KubeNodeManager实例
func NewKubeNodeManager(nodeIPs []string, deployMode constants.DeployModeType, baseDir string) (NodeManagerInterface, error) {
	return &KubeNodeManager{
		NodeIPs:    nodeIPs,
		DeployMode: deployMode,
		BaseDir:    baseDir,
	}, nil
}

// Init 初始化节点
func (k *KubeNodeManager) Init(ctx context.Context) error {
	// 查找所有脚本
	scripts, err := util.FindNodeScripts(k.<PERSON>ploy<PERSON>, k.BaseDir)
	if err != nil {
		return fmt.Errorf("failed to find node scripts in base directory %s: %w", k.BaseDir, err)
	}
	logger.Debugf(ctx, "Found node scripts: %v", scripts)

	// 创建 errgroup
	g, ctx := errgroup.WithContext(ctx)

	// 并发执行所有节点的初始化脚本
	for _, script := range scripts {
		script := script // 创建副本避免闭包问题
		for _, nodeIP := range k.NodeIPs {
			nodeIP := nodeIP // 创建副本避免闭包问题
			g.Go(func() error {
				return nodeInit(ctx, nodeIP, script, filepath.Dir(script))
			})
		}
	}

	// 等待所有任务完成并收集错误
	if err := g.Wait(); err != nil {
		return fmt.Errorf("errors occurred during node initialization: %w", err)
	}

	return nil
}

// nodeInit 在指定节点上执行初始化脚本
func nodeInit(ctx context.Context, nodeIP string, scriptPath string, workDir string) error {
	logger.Infof(ctx, "Starting initialization script execution on node %s", nodeIP)

	client, err := sshpass.NewSSHNoPassword("root", nodeIP)
	if err != nil {
		logger.Errorf(ctx, "Failed to create SSH client for node %s: %v", nodeIP, err)
		return fmt.Errorf("failed to create SSH client: %w", err)
	}

	// 构建并执行命令
	cmd := fmt.Sprintf("cd %s && bash %s", workDir, scriptPath)
	output, err := client.WithNotTimeout().ExecRemoteCmd(ctx, cmd)

	if err != nil {
		logger.Errorf(ctx, "Failed to execute script on node %s: %v, output: %s", nodeIP, err, output)
		return fmt.Errorf("failed to execute script: %w", err)
	}

	logger.Infof(ctx, "Successfully completed initialization script execution on node %s, output: %s", nodeIP, output)
	return nil
}
